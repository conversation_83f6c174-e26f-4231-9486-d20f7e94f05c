# SAM Everything 简化版 (SAME)

## 概述

SAM Everything 简化版是一个专注于对象提取的工具，提供了简化的处理流程和增强的文件管理功能。

### 主要特性

✅ **简化的处理流程** - 直接使用SAM Everything进行自动分割，跳过复杂的分类步骤  
✅ **自动对象提取** - 将所有检测到的对象自动裁剪并保存为独立图像  
✅ **增强的文件管理** - 图像文件列表，点击加载，批量处理  
✅ **更大的图像显示** - 提高可视性，更好的用户体验  
✅ **中文路径支持** - 完美支持包含中文字符的文件路径  
✅ **批量处理能力** - 一次处理整个文件夹的图像  

## 文件结构

```
SAME/
├── sam_everything_simplified.py          # 核心处理类
├── sam_everything_gui_simplified.py      # GUI界面
├── quick_fix_chinese_path.py             # 中文路径修复工具
├── README.md                             # 本文件
└── sam_vit_h_4b8939.pth                 # SAM模型文件（需要下载）
```

## 快速开始

### 1. 环境准备

```bash
# 安装基础依赖
pip install opencv-python numpy pillow

# 安装SAM依赖
pip install segment-anything torch torchvision

# 下载SAM模型文件
# 将 sam_vit_h_4b8939.pth 放在SAME目录中
```

### 2. 启动GUI界面

```bash
cd SAME
python sam_everything_gui_simplified.py
```

### 3. 基本使用流程

1. **选择文件/文件夹**
   - 点击"选择文件夹"浏览整个目录
   - 或点击"选择单个文件"处理单张图像

2. **浏览图像文件**
   - 左侧列表显示所有图像文件
   - 点击文件名即可加载图像

3. **调整参数**（可选）
   - 每边点数：控制分割密度
   - 最小对象面积：过滤小对象
   - 可视化选项：掩膜显示和透明度

4. **处理图像**
   - "处理当前图像"：处理选中的图像
   - "批量处理所有图像"：处理文件夹中所有图像

5. **查看结果**
   - 对象文件：object_001.jpg, object_002.jpg, ...
   - 结果图像：原图名_result.jpg

## 中文路径支持

如果遇到中文路径问题，运行修复工具：

```bash
python quick_fix_chinese_path.py
```

该工具会：
- 自动检测中文路径问题
- 将图像文件复制到安全的英文路径
- 生成使用说明

## 输出结果

```
output/
├── image1/                    # 以原图名命名的子目录
│   ├── object_001.jpg        # 第1个检测对象
│   ├── object_002.jpg        # 第2个检测对象
│   ├── ...
│   └── image1_result.jpg     # 可视化结果图像
├── image2/
│   ├── object_001.jpg
│   ├── ...
└── ...
```

## 配置选项

### 默认配置
- 每边点数：32
- 最小对象面积：100像素
- 显示掩膜：开启
- 掩膜透明度：0.35

### 参数调优建议
- **高质量模式**：每边点数64，最小面积200
- **快速模式**：每边点数16，最小面积50
- **大对象模式**：最小面积1000

## 系统要求

- Python 3.7+
- 8GB+ 内存（推荐16GB）
- CUDA支持的GPU（可选，但强烈推荐）

## 故障排除

### 常见问题

1. **中文路径问题**
   ```bash
   python quick_fix_chinese_path.py
   ```

2. **内存不足**
   - 降低每边点数
   - 处理更小的图像

3. **处理速度慢**
   - 使用GPU
   - 降低分割密度

4. **对象过多**
   - 提高最小对象面积

### 检查环境
```bash
python -c "import cv2, numpy, PIL; print('基础库正常')"
python -c "import torch; print('PyTorch正常')"
python -c "from segment_anything import sam_model_registry; print('SAM正常')"
```

## 使用场景

- **科学研究**：种子、细胞、颗粒等对象的自动提取
- **数据集制作**：从大图像中提取小对象
- **质量检测**：产品缺陷检测，零件分拣
- **图像分析**：医学图像、遥感图像、显微镜图像分析

## 技术特点

- **零配置使用**：默认参数即可获得良好效果
- **文件夹浏览**：像文件管理器一样浏览图像
- **一键批处理**：处理整个文件夹的图像
- **实时预览**：更大的图像显示区域
- **自动命名**：规范的文件命名和目录结构
- **中文路径兼容**：完美支持中文文件路径

---

**简化版特点总结**：
- ✅ 更简单的工作流程
- ✅ 更好的文件管理
- ✅ 更大的图像显示
- ✅ 更强的批量处理能力
- ✅ 更直观的用户界面
- ✅ 完整的中文路径支持
